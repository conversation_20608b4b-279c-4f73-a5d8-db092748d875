# Laika Control Panel - PrimeNG Custom Theme

## Overview

This directory contains the custom PrimeNG theme for the Laika Control Panel application. The theme is built using PrimeNG's `definePreset` methodology, which provides a clean and maintainable way to customize the appearance of all PrimeNG components.

## Theme Structure

### Main Theme File
- `laika-control-panel-preset.ts` - The main theme preset file that defines all customizations

### Color Palette

The theme uses the brand colors defined in `src/assets/styles/_variables.scss`:

- **Primary Blue 01**: `#1E0E70` - Main brand color
- **Primary Blue 02**: `#5A59AC` - Secondary brand color  
- **Primary Blue 03**: `#A8A8F6` - Light brand color
- **Primary Blue 04**: `#C3C3FF` - Lightest brand color
- **Primary Grey**: `#ACACAC` - Neutral color
- **Neutral Light Grey**: `#EFF0F7E5` - Background color

### Extended Palette

The theme extends these colors into a full palette (50-950) for comprehensive theming:

```typescript
primary: {
  50: '#f0f0ff',
  100: '#e0e0ff',
  200: '#c7c7ff',
  300: '#a5a5ff',
  400: '#8080ff',
  500: '#1E0E70', // Main brand color
  600: '#1a0c63',
  700: '#160a56',
  800: '#120849',
  900: '#0e063c',
  950: '#0a042f'
}
```

## Customized Components

The theme provides comprehensive styling for all PrimeNG components used in the application:

### 1. Button Component
- **Border Radius**: 7px (rounded corners)
- **Font Family**: SoleSans-SemiBold
- **Colors**: Primary brand colors with proper hover/active states
- **Focus Ring**: Custom focus styling with brand colors

### 2. Card Component
- **Border Radius**: 12px
- **Shadow**: Subtle box shadow (0 4px 6px rgba(0, 0, 0, 0.1))
- **Header**: No divider between header and body
- **Padding**: Optimized spacing for better visual hierarchy

### 3. Input Components (InputText & Password)
- **Background**: Transparent
- **Border**: 1px solid with brand colors
- **Border Radius**: 10px
- **Height**: 50px
- **Padding**: 1.5rem horizontal, 0.75rem vertical
- **Focus State**: Brand color border with custom shadow
- **Placeholder**: Subtle grey color

### 4. Message Component
- **Border Radius**: 8px
- **Error State**: Custom red background and border
- **Consistent**: Padding and typography

### 5. ProgressSpinner Component
- **Color**: Single brand color (#1E0E70)
- **No Gradients**: Clean, single-color spinner

### 6. Accordion Component
- **Border**: Clean borders with brand colors
- **Header**: Custom padding and typography
- **Active State**: Brand color highlighting
- **Smooth Transitions**: Enhanced user experience

## Key Features

### 1. Design Token Architecture
- Uses PrimeNG's semantic token system
- Consistent color mapping across all components
- Easy maintenance and updates

### 2. Brand Consistency
- All colors derived from brand variables
- Consistent typography using SoleSans font family
- Unified spacing and sizing

### 3. Accessibility
- Proper focus ring styling
- High contrast ratios
- Clear visual hierarchy

### 4. Responsive Design
- Works seamlessly across all screen sizes
- Consistent appearance on mobile and desktop

## Usage

The theme is automatically applied to all PrimeNG components in the application. No additional configuration is needed for individual components.

### Applying the Theme

The theme is configured in `src/main.ts`:

```typescript
import laikaControlPanelPreset from './themes/laika-control-panel-preset';

providePrimeNG({
  theme: {
    preset: laikaControlPanelPreset,
    options: {
      prefix: 'p',
      darkModeSelector: 'system',
      cssLayer: false
    }
  }
})
```

### Dark Mode Support

The theme includes comprehensive dark mode support that can be activated by changing the `darkModeSelector` option in the configuration.

## Maintenance

### Adding New Components

When adding new PrimeNG components to the application:

1. Add the component configuration to the `components` section in `laika-control-panel-preset.ts`
2. Use the existing semantic tokens for consistency
3. Follow the established patterns for colors, spacing, and typography

### Updating Colors

To update brand colors:

1. Modify the colors in `src/assets/styles/_variables.scss`
2. Update the corresponding values in the theme preset
3. The changes will automatically apply to all components

### Best Practices

- Always use semantic tokens when possible
- Maintain consistency with existing component patterns
- Test changes across all components and screen sizes
- Document any new customizations

## Migration from CSS Overrides

This theme replaces the previous CSS-based customization approach (`primeng-custom-theme.scss`). The new approach provides:

- Better maintainability
- Consistent theming across all components
- Easier updates and modifications
- Better performance
- Type safety with TypeScript

## Support

For questions or issues related to the theme, refer to:
- [PrimeNG Theming Documentation](https://primeng.org/theming)
- [PrimeNG definePreset Guide](https://primeng.org/theming#definepreset)
