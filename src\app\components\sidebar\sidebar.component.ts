import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-sidebar',
  imports: [RouterModule, CommonModule, ButtonModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
export class SidebarComponent {
  sidebarCollapsed = false;

  routes = [
    {
      icon: 'i-document',
      path: '/reports',
      text: 'Reports',
      visible: true,
    },
    {
      icon: 'i-paper-clip',
      path: '/integrations',
      text: 'Integrations',
      visible: true,
    },
  ];

  constructor(public router: Router) {}

  toggleSidebar() {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }
}
