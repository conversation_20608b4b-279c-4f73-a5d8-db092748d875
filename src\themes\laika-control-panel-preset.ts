import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

const laikaControlPanelPreset = definePreset(Aura, {
  // Primary color palette using Laika brand colors
  semantic: {
    primary: {
      50: '#f0f0ff',
      100: '#e0e0ff',
      200: '#c7c7ff',
      300: '#a5a5ff',
      400: '#8080ff',
      500: '#1E0E70', // Main brand color
      600: '#1a0c63',
      700: '#160a56',
      800: '#120849',
      900: '#0e063c',
      950: '#0a042f'
    },
    // Focus ring configuration
    focusRing: {
      width: '2px',
      style: 'solid',
      color: '{primary.500}',
      offset: '2px'
    },
    // Color scheme configuration for light and dark modes
    colorScheme: {
      light: {
        // Primary colors for light mode
        primary: {
          color: '{primary.500}',
          contrastColor: '#ffffff',
          hoverColor: '{primary.600}',
          activeColor: '{primary.700}'
        },
        // Form field styling
        formField: {
          background: 'transparent',
          disabledBackground: '{surface.200}',
          filledBackground: 'transparent',
          filledHoverBackground: 'transparent',
          filledFocusBackground: 'transparent',
          borderColor: '#ACACAC', // Using $primary-grey
          hoverBorderColor: '{primary.500}',
          focusBorderColor: '{primary.500}',
          invalidBorderColor: '{red.500}',
          color: '{surface.700}',
          disabledColor: '{surface.500}',
          placeholderColor: '{surface.500}',
          floatLabelColor: '{surface.500}',
          floatLabelFocusColor: '{primary.500}',
          floatLabelActiveColor: '{primary.500}',
          floatLabelInvalidColor: '{red.500}',
          iconColor: '{surface.500}',
          shadow: '0 0 0 0.2rem rgba(30, 14, 112, 0.25)'
        },
        // Surface colors
        surface: {
          0: '#ffffff',
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
          300: '#e0e0e0',
          400: '#bdbdbd',
          500: '#9e9e9e',
          600: '#757575',
          700: '#616161',
          800: '#424242',
          900: '#212121',
          950: '#0f0f0f'
        }
      },
      dark: {
        // Primary colors for dark mode
        primary: {
          color: '{primary.400}',
          contrastColor: '{primary.950}',
          hoverColor: '{primary.300}',
          activeColor: '{primary.200}'
        },
        // Form field styling for dark mode
        formField: {
          background: 'transparent',
          disabledBackground: '{surface.700}',
          filledBackground: 'transparent',
          filledHoverBackground: 'transparent',
          filledFocusBackground: 'transparent',
          borderColor: '{surface.600}',
          hoverBorderColor: '{primary.400}',
          focusBorderColor: '{primary.400}',
          invalidBorderColor: '{red.400}',
          color: '{surface.0}',
          disabledColor: '{surface.400}',
          placeholderColor: '{surface.400}',
          floatLabelColor: '{surface.400}',
          floatLabelFocusColor: '{primary.400}',
          floatLabelActiveColor: '{primary.400}',
          floatLabelInvalidColor: '{red.400}',
          iconColor: '{surface.400}',
          shadow: '0 0 0 0.2rem rgba(168, 168, 246, 0.25)'
        },
        // Surface colors for dark mode
        surface: {
          0: '#ffffff',
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
          300: '#e0e0e0',
          400: '#bdbdbd',
          500: '#9e9e9e',
          600: '#757575',
          700: '#616161',
          800: '#424242',
          900: '#212121',
          950: '#0f0f0f'
        }
      }
    }
  },
  // Component-specific customizations
  components: {
    // Button component styling
    button: {
      root: {
        borderRadius: '7px',
        fontFamily: 'SoleSans-SemiBold, sans-serif',
        fontWeight: '600',
        paddingX: '1.5rem',
        paddingY: '0.75rem',
        gap: '0.5rem',
        transitionDuration: '{transition.duration}'
      },
      colorScheme: {
        light: {
          root: {
            primary: {
              background: '{primary.500}',
              hoverBackground: '{primary.600}',
              activeBackground: '{primary.700}',
              borderColor: '{primary.500}',
              hoverBorderColor: '{primary.600}',
              activeBorderColor: '{primary.700}',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
              focusRing: {
                color: 'rgba(30, 14, 112, 0.25)',
                shadow: '0 0 0 0.2rem rgba(30, 14, 112, 0.25)'
              }
            },
            secondary: {
              background: 'transparent',
              hoverBackground: '{surface.100}',
              activeBackground: '{surface.200}',
              borderColor: '{surface.300}',
              hoverBorderColor: '{surface.400}',
              activeBorderColor: '{surface.500}',
              color: '{surface.700}',
              hoverColor: '{surface.800}',
              activeColor: '{surface.900}'
            }
          }
        },
        dark: {
          root: {
            primary: {
              background: '{primary.400}',
              hoverBackground: '{primary.300}',
              activeBackground: '{primary.200}',
              borderColor: '{primary.400}',
              hoverBorderColor: '{primary.300}',
              activeBorderColor: '{primary.200}',
              color: '{primary.950}',
              hoverColor: '{primary.950}',
              activeColor: '{primary.950}',
              focusRing: {
                color: 'rgba(168, 168, 246, 0.25)',
                shadow: '0 0 0 0.2rem rgba(168, 168, 246, 0.25)'
              }
            }
          }
        }
      }
    },
    // Card component styling
    card: {
      root: {
        background: '{surface.0}',
        borderRadius: '12px',
        color: '{surface.700}',
        shadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        border: '1px solid {surface.200}'
      },
      header: {
        background: 'transparent',
        borderColor: 'transparent',
        borderWidth: '0',
        borderRadius: '0',
        color: '{surface.700}',
        padding: '1.5rem 1.5rem 0 1.5rem'
      },
      body: {
        padding: '1rem 1.5rem 1.5rem 1.5rem',
        gap: '0.5rem'
      },
      footer: {
        background: 'transparent',
        borderColor: 'transparent',
        color: '{surface.700}',
        padding: '0 1.5rem 1.5rem 1.5rem'
      },
      title: {
        fontWeight: '600',
        fontSize: '1.25rem'
      },
      subtitle: {
        color: '{surface.500}',
        fontSize: '0.875rem'
      }
    },
    // InputText component styling
    inputtext: {
      root: {
        background: 'transparent',
        disabledBackground: '{surface.200}',
        filledBackground: 'transparent',
        filledHoverBackground: 'transparent',
        filledFocusBackground: 'transparent',
        borderColor: '#ACACAC',
        hoverBorderColor: '{primary.500}',
        focusBorderColor: '{primary.500}',
        invalidBorderColor: '{red.500}',
        color: '{surface.700}',
        disabledColor: '{surface.500}',
        placeholderColor: '{surface.500}',
        shadow: 'none',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'transparent',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(30, 14, 112, 0.25)'
        },
        borderRadius: '10px',
        borderWidth: '1px',
        paddingX: '1.5rem',
        paddingY: '0.75rem',
        fontSize: '1rem',
        fontFamily: 'inherit',
        fontWeight: 'normal',
        height: '50px'
      }
    },
    // Password component styling
    password: {
      meter: {
        background: '{surface.100}',
        borderRadius: '{border.radius.xs}',
        height: '.75rem'
      },
      icon: {
        color: '#ACACAC',
        hoverColor: '{primary.500}'
      },
      overlay: {
        background: '{overlay.modal.background}',
        borderColor: '{overlay.modal.border.color}',
        borderRadius: '{overlay.modal.border.radius}',
        color: '{overlay.modal.color}',
        padding: '{overlay.modal.padding}',
        shadow: '{overlay.modal.shadow}'
      },
      content: {
        gap: '0.5rem'
      },
      colorScheme: {
        light: {
          strength: {
            weakBackground: '{red.500}',
            mediumBackground: '{amber.500}',
            strongBackground: '{green.500}'
          }
        },
        dark: {
          strength: {
            weakBackground: '{red.400}',
            mediumBackground: '{amber.400}',
            strongBackground: '{green.400}'
          }
        }
      }
    },
    // Message component styling
    message: {
      root: {
        borderRadius: '8px',
        borderWidth: '1px',
        fontSize: '1rem',
        fontWeight: 'normal',
        padding: '1rem 1.25rem',
        gap: '0.5rem'
      },
      text: {
        fontSize: '1rem',
        fontWeight: 'normal'
      },
      icon: {
        size: '1.125rem'
      },
      colorScheme: {
        light: {
          info: {
            background: '{blue.50}',
            borderColor: '{blue.200}',
            color: '{blue.600}',
            shadow: 'none'
          },
          success: {
            background: '{green.50}',
            borderColor: '{green.200}',
            color: '{green.600}',
            shadow: 'none'
          },
          warn: {
            background: '{yellow.50}',
            borderColor: '{yellow.200}',
            color: '{yellow.600}',
            shadow: 'none'
          },
          error: {
            background: '#fef2f2',
            borderColor: '#fecaca',
            color: '#dc2626',
            shadow: 'none'
          },
          secondary: {
            background: '{surface.100}',
            borderColor: '{surface.200}',
            color: '{surface.600}',
            shadow: 'none'
          },
          contrast: {
            background: '{surface.900}',
            borderColor: '{surface.950}',
            color: '{surface.50}',
            shadow: 'none'
          }
        }
      }
    },
    // ProgressSpinner component styling
    progressspinner: {
      root: {
        'color.1': '{primary.500}',
        'color.2': 'transparent',
        'color.3': 'transparent',
        'color.4': 'transparent'
      }
    },
    // Accordion component styling
    accordion: {
      root: {
        transitionDuration: '{transition.duration}'
      },
      panel: {
        borderWidth: '1px',
        borderColor: '{surface.200}'
      },
      header: {
        color: '{surface.700}',
        hoverColor: '{surface.800}',
        activeColor: '{primary.500}',
        padding: '1.125rem',
        fontWeight: '600',
        borderRadius: '0',
        borderWidth: '0',
        borderColor: 'transparent',
        background: 'transparent',
        hoverBackground: '{surface.50}',
        activeBackground: 'transparent',
        activeHoverBackground: '{surface.50}',
        focusRing: {
          width: '{focus.ring.width}',
          style: '{focus.ring.style}',
          color: '{focus.ring.color}',
          offset: '{focus.ring.offset}',
          shadow: '{focus.ring.shadow}'
        },
        toggleIcon: {
          color: '{surface.600}',
          hoverColor: '{surface.700}',
          activeColor: '{primary.500}',
          activeHoverColor: '{primary.600}'
        },
        first: {
          topBorderRadius: '{content.border.radius}',
          borderWidth: '1px 1px 0 1px'
        },
        last: {
          bottomBorderRadius: '{content.border.radius}',
          activeBottomBorderRadius: '0'
        }
      },
      content: {
        borderWidth: '0 1px 1px 1px',
        borderColor: '{surface.200}',
        background: '{surface.0}',
        color: '{surface.700}',
        padding: '1.125rem'
      }
    }
  }
});

export default laikaControlPanelPreset;
